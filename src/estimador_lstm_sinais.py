#!/usr/bin/env python3
"""
Script para estimativa de sinais de compra e venda usando LSTM
Baseado nas mesmas features do estimador XGBoost, importando de features_xgboost.py
Utiliza arquitetura LSTM para previsão sequencial de sinais binários (0=Venda, 1=Compra)
"""

import os
import sys
import random
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from scipy.stats import norm
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping

# Fixar seeds para reprodutibilidade
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)
tf.random.set_seed(RANDOM_SEED)
os.environ['PYTHONHASHSEED'] = str(RANDOM_SEED)

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment
from features_xgboost import calcular_features_e_sinais
from classificador_xgboost_sinais import (
 carregar_acoes_diversificadas,
 baixar_dados_todas_acoes_unificado,
 baixar_dados_acao_otimizado,
 baixar_dados_acao,
 verificar_status_cache,
 limpar_cache_historico
)
warnings.filterwarnings('ignore')
# Função para carregar ações diversificadas (mesmo padrão do XGBoost)
def carregar_acoes_diversificadas():
    file_paths = config.get_file_paths()
    csv_path = file_paths['acoes_diversificacao']
    df = pd.read_csv(csv_path)
    acoes = []
    for _, row in df.iterrows():
        if pd.notna(row['Ticker']) and str(row['Ticker']).strip():
            ticker = str(row['Ticker']).strip() + '.SA'
            nome = row['Nome'] if 'Nome' in row and pd.notna(row['Nome']) else ticker
            acoes.append((ticker, nome))
    print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
    return acoes


def preparar_dataset_regressao(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento de regressão
    Preserva o índice de data para divisão temporal
    Target: variação percentual da média OHLC em relação ao dia anterior
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados_copy = dados_copy.reset_index()
            datasets.append(dados_copy)

    # Combinar todos os datasets preservando as datas
    dataset_completo = pd.concat(datasets, ignore_index=True)

    # Converter coluna Date para datetime se necessário
    if 'Date' in dataset_completo.columns:
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])
    elif dataset_completo.index.name == 'Date' or isinstance(dataset_completo.index, pd.DatetimeIndex):
        dataset_completo = dataset_completo.reset_index()
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])

    # Features: usar exatamente as mesmas features do classificador
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')

    # Features básicas (REMOVIDAS features do dia atual que usam OHLCV)
    # Mantém apenas features lagged e features temporais
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    quarter_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    holiday_features = ['Pre_Feriado_Brasil']

    # APENAS features lagged da média OHLC (sem features do dia atual)
    basic_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)] + weekday_features + month_features + quarter_features + holiday_features

    # Features econométricas lagged (APENAS versões lagged)
    econometric_features_all = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
        'High_Max_50', 'Low_Min_50'
    ]

    econometric_features_lagged = []
    for feature in econometric_features_all:
        for i in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{i}')

    # Combinar apenas features lagged e temporais (SEM features do dia atual)
    feature_cols = basic_features + econometric_features_lagged

    X = dataset_completo[feature_cols]

    # NOVO TARGET: Variação percentual da média OHLC em relação ao dia anterior
    # Calcular variação percentual: (valor_hoje - valor_ontem) / valor_ontem * 100
    dataset_completo = dataset_completo.sort_values(['Ticker', 'Date'])
    dataset_completo['Media_OHLC_Anterior'] = dataset_completo.groupby('Ticker')['Media_OHLC'].shift(1)

    # Calcular variação percentual
    mask_valido = (dataset_completo['Media_OHLC_Anterior'] > 0) & (~dataset_completo['Media_OHLC_Anterior'].isna())
    dataset_completo['Target_PctChange'] = np.nan
    dataset_completo.loc[mask_valido, 'Target_PctChange'] = (
        (dataset_completo.loc[mask_valido, 'Media_OHLC'] - dataset_completo.loc[mask_valido, 'Media_OHLC_Anterior']) /
        dataset_completo.loc[mask_valido, 'Media_OHLC_Anterior'] * 100
    )

    # Target: variação percentual
    y = dataset_completo['Target_PctChange']

    # Remover registros com NaN no target
    mask_validos = ~y.isna()
    X_filtrado = X[mask_validos].copy()
    y_filtrado = y[mask_validos].copy()
    dataset_filtrado = dataset_completo[mask_validos].copy()

    print(f"📊 Dataset de regressão preparado:")
    print(f"   • Total de registros: {len(y_filtrado)}")
    print(f"   • Features utilizadas: {len(feature_cols)}")
    print(f"   • Target: Variação % OHLC (min: {y_filtrado.min():.2f}%, max: {y_filtrado.max():.2f}%, média: {y_filtrado.mean():.2f}%)")

    return X_filtrado, y_filtrado, feature_cols, dataset_filtrado


def dividir_dados_temporal_regressao(dataset_completo, feature_cols, y):
    """
    Divide os dados temporalmente baseado na configuração para regressão
    """
    # Ordenar por data
    dataset_ordenado = dataset_completo.sort_values('Date').copy()
    y_ordenado = y[dataset_ordenado.index]
    test_size = config.get('xgboost.test_size')

    # Obter configurações de divisão temporal
    data_max = dataset_ordenado['Date'].max()
    data_min = dataset_ordenado['Date'].min()
    years_range = data_max.year - data_min.year
    test_years = int(years_range * test_size)
    data_corte = data_max - pd.DateOffset(years=test_years)

    print(f"📅 Divisão temporal dos dados (regressão):")
    print(f"   • Data máxima: {data_max.strftime('%Y-%m-%d')}")
    print(f"   • Data de corte: {data_corte.strftime('%Y-%m-%d')}")
    print(f"   • Configuração: {test_years} ano(s) teste")

    # Dividir dados
    mask_treino = dataset_ordenado['Date'] <= data_corte
    mask_teste = dataset_ordenado['Date'] > data_corte

    dados_treino = dataset_ordenado[mask_treino]
    dados_teste = dataset_ordenado[mask_teste]

    print(f"   • Dados de treino: {len(dados_treino)} registros ({dados_treino['Date'].min().strftime('%Y-%m-%d')} a {dados_treino['Date'].max().strftime('%Y-%m-%d')})")
    print(f"   • Dados de teste: {len(dados_teste)} registros ({dados_teste['Date'].min().strftime('%Y-%m-%d')} a {dados_teste['Date'].max().strftime('%Y-%m-%d')})")

    # Extrair features e targets
    X_train = dados_treino[feature_cols]
    X_test = dados_teste[feature_cols]
    y_train = y_ordenado[mask_treino]
    y_test = y_ordenado[mask_teste]

    return X_train, X_test, y_train, y_test


def preparar_dados_lstm(X, y, sequence_length=20):
    """
    Prepara os dados para entrada no LSTM (sequências) para regressão
    """
    X_seq, y_seq = [], []
    for i in range(len(X) - sequence_length):
        X_seq.append(X.iloc[i:(i + sequence_length)].values)
        y_seq.append(y.iloc[i + sequence_length])
    return np.array(X_seq), np.array(y_seq)


def treinar_lstm_regressao(X_train, y_train, X_test, y_test, input_shape, epochs=50, batch_size=32):
    """
    Treina um modelo LSTM para regressão
    """
    model = Sequential()
    model.add(LSTM(config.get('lstm.units', 50), input_shape=input_shape, return_sequences=False))
    model.add(Dropout(config.get('lstm.dropout', 0.2)))
    model.add(Dense(config.get('lstm.dense_units', 25), activation='relu'))
    model.add(Dense(1)) # Output layer for regression
    model.compile(optimizer='adam', loss='mean_squared_error') # Use MSE for regression
    early_stop = EarlyStopping(monitor='val_loss', patience=config.get('lstm.patience', 10), restore_best_weights=True)
    history = model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stop],
        verbose=2
    )

    # Fazer predições
    y_pred = model.predict(X_test).flatten()

    # Calcular métricas de regressão
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print(f"   • RMSE: {rmse:.4f}")
    print(f"   • MAE: {mae:.4f}")
    print(f"   • R²: {r2:.4f}")

    resultados = {
        'modelo': model,
        'scaler': None, # Scaler handled separately
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'y_test': y_test,
        'y_pred': y_pred,
        'history': history
    }

    return resultados


def aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols, scaler, sequence_length):
    """
    Aplica as predições do estimador LSTM aos dados de cada ação
    Converte predições de variação percentual de volta para valores OHLC
    Gera sinais de compra/venda baseados no valor estimado vs valor anterior
    """
    modelo = resultados_modelo['modelo']
    threshold = config.get('xgboost.features.pct_threshold')  # Usar mesmo threshold do classificador

    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > sequence_length:
            dados_copy = dados.copy()

            # Verificar se todas as features existem
            features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
            if len(features_disponiveis) != len(feature_cols):
                print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                continue

            # Pegar os últimos 'sequence_length' dias para predição
            X_pred = dados_copy[feature_cols].tail(sequence_length)
            
            if len(X_pred) < sequence_length:
                continue # Not enough data for sequence

            # Aplicar scaler
            X_scaled = scaler.transform(X_pred)
            X_scaled = X_scaled.reshape(1, sequence_length, len(feature_cols)) # Reshape for LSTM

            # Fazer predição da VARIAÇÃO PERCENTUAL
            pct_change_estimado_modelo = modelo.predict(X_scaled)[0][0]

            # Obter valor do dia ATUAL (HOJE) para a decisão
            preco_atual = dados_copy['Media_OHLC'].iloc[-1]

            # CONVERTER DE VOLTA PARA VALOR OHLC ESTIMADO
            if preco_atual > 0:
                valor_estimado = preco_atual * (1 + pct_change_estimado_modelo / 100)
            else:
                valor_estimado = 0

            # Calcular a variação percentual para a DECISÃO e EXIBIÇÃO
            if preco_atual > 0:
                pct_change_final = ((valor_estimado / preco_atual) - 1) * 100
            else:
                pct_change_final = 0.0

            # Gerar sinais baseados no threshold
            if pct_change_final > threshold:
                sinal_compra = 1
                sinal_venda = 0
                sinal_tipo = "COMPRA"
            elif pct_change_final < -threshold:
                sinal_compra = 0
                sinal_venda = 1
                sinal_tipo = "VENDA"
            else:
                sinal_compra = 0
                sinal_venda = 0
                sinal_tipo = "SEM_SINAL"

            # Adicionar predições aos dados (apenas para o último dia)
            dados_copy.loc[dados_copy.index[-1], 'Valor_Estimado'] = valor_estimado
            dados_copy.loc[dados_copy.index[-1], 'Pct_Change_Estimado'] = pct_change_final
            dados_copy.loc[dados_copy.index[-1], 'Pred_Compra'] = sinal_compra
            dados_copy.loc[dados_copy.index[-1], 'Pred_Venda'] = sinal_venda
            dados_copy.loc[dados_copy.index[-1], 'Sinal_Tipo'] = sinal_tipo

            # Preencher NaN com valores padrão
            dados_copy['Valor_Estimado'] = dados_copy['Valor_Estimado'].fillna(0.0)
            dados_copy['Pct_Change_Estimado'] = dados_copy['Pct_Change_Estimado'].fillna(0.0)
            dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
            dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
            dados_copy['Sinal_Tipo'] = dados_copy['Sinal_Tipo'].fillna("SEM_SINAL")

            acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    print(f"   📊 Modelo prediz variação % e converte para valor OHLC")
    print(f"   📊 Threshold utilizado: {threshold}% (mesmo do classificador)")

    return acoes_com_predicoes


def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo carteira.csv
    Retorna um dicionário com ticker -> quantidade atual
    """
    try:
        carteira_df = pd.read_csv('carteira.csv')

        # Calcular posição atual de cada ticker
        carteira_atual = {}
        for _, row in carteira_df.iterrows():
            ticker = row['ticker']
            quantidade = row['quantidade']

            if ticker in carteira_atual:
                carteira_atual[ticker] += quantidade
            else:
                carteira_atual[ticker] = quantidade

        # Filtrar apenas tickers com quantidade > 0
        carteira_atual = {ticker: qtd for ticker, qtd in carteira_atual.items() if qtd > 0}

        print(f"📋 Carteira atual carregada: {len(carteira_atual)} posições ativas")
        for ticker, qtd in carteira_atual.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira_atual

    except FileNotFoundError:
        print(f"⚠️ Arquivo carteira.csv não encontrado - mostrando todos os sinais de venda")
        return {}
    except Exception as e:
        print(f"⚠️ Erro ao carregar carteira: {e} - mostrando todos os sinais de venda")
        return {}


def imprimir_recomendacoes_estimador(acoes_com_predicoes):
    """
    Imprime recomendações de compra e venda baseadas no estimador
    Sinais de venda apenas para ações na carteira atual
    Segue o mesmo formato do classificador XGBoost
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - ESTIMADOR LSTM")
    print(f"=" * 60)

    # Verificar data mais recente disponível
    data_mais_recente = None
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultima_data = dados.index.max()
            if data_mais_recente is None or ultima_data > data_mais_recente:
                data_mais_recente = ultima_data

    if data_mais_recente:
        print(f"📅 Análise baseada nos dados até: {data_mais_recente.strftime('%d/%m/%Y (%A)')}")
        print(f"🤖 Sinais baseados nas estimativas do modelo LSTM (regressão)")
        print(f"🎯 Threshold: {config.get('xgboost.features.pct_threshold')}%")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Coletar sinais
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')
            ultimo_dia = dados.tail(1).iloc[0]

            # Verificar se há sinais
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker_clean': ticker_clean,
                    'nome': ticker_clean,  # Nome simplificado
                    'preco': ultimo_dia.get('Media_OHLC', 0),
                    'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                    'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'spread': ultimo_dia.get('Spread', 0),
                    'data': ultimo_dia.name
                })
            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas mostrar sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker_clean': ticker_clean,
                        'nome': ticker_clean,  # Nome simplificado
                        'preco': ultimo_dia.get('Media_OHLC', 0),
                        'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                        'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'quantidade_carteira': carteira_atual[ticker]
                    })

    # Exibir sinais de venda PRIMEIRO (ordenados por maior queda estimada)
    # APENAS para ações na carteira atual
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações na carteira):")
        print("-" * 60)
        # Ordenar por maior queda estimada (mais negativo primeiro)
        for sinal in sorted(sinais_venda, key=lambda x: x['pct_change_estimado']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            quantidade = sinal.get('quantidade_carteira', 0)
            print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) - {quantidade} ações na carteira")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Valor estimado: R$ {sinal['valor_estimado']:.2f} ({sinal['pct_change_estimado']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 Estimador prevê queda no preço")
            print()
    else:
        print(f"\n🔴 SINAIS DE VENDA: Nenhuma ação na carteira com sinal de venda")
        print("-" * 60)

    # Exibir sinais de compra DEPOIS (ordenados por maior alta estimada)
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        # Ordenar por maior alta estimada (mais positivo primeiro)
        for sinal in sorted(sinais_compra, key=lambda x: x['pct_change_estimado'], reverse=True):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Valor estimado: R$ {sinal['valor_estimado']:.2f} ({sinal['pct_change_estimado']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 Estimador prevê alta no preço")
            print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🔴 Venda: {len(sinais_venda)} ações na carteira (ordenadas por maior queda estimada)")
    print(f"   🟢 Compra: {len(sinais_compra)} ações (ordenadas por maior alta estimada)")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")
    print(f"   🎯 Threshold: {config.get('xgboost.features.pct_threshold')}%")
    if carteira_atual:
        print(f"   📋 Carteira atual: {len(carteira_atual)} posições ativas")


def criar_graficos_desempenho_estimador(resultados_modelo):
    """
    Cria gráficos para avaliar o desempenho do estimador LSTM
    """
    print(f"\n📊 Criando gráficos de desempenho do estimador...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lstm_estimador_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # Extrair dados dos resultados
    y_test = resultados_modelo['y_test']
    y_pred = resultados_modelo['y_pred']
    rmse = resultados_modelo['rmse']
    mae = resultados_modelo['mae']
    r2 = resultados_modelo['r2']

    # Configurar estilo dos gráficos
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10

    # 1. Gráfico: Valores Reais vs Estimados (Scatter Plot)
    fig, ax = plt.subplots(figsize=(10, 8))

    # Scatter plot
    ax.scatter(y_test, y_pred, alpha=0.6, color='steelblue', s=20)

    # Linha de referência perfeita (y = x)
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Predição Perfeita')

    # Configurações do gráfico
    ax.set_xlabel('Variação % Real', fontsize=12)
    ax.set_ylabel('Variação % Estimada', fontsize=12)
    ax.set_title('Estimador LSTM: Variação % Real vs Estimada\n' +
                f'R² = {r2:.4f} | RMSE = {rmse:.2f}% | MAE = {mae:.2f}%',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Adicionar estatísticas no gráfico
    ax.text(0.05, 0.95, f'Pontos: {len(y_test)}\nR²: {r2:.4f}\nRMSE: {rmse:.2f}\nMAE: {mae:.2f}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'valores_reais_vs_estimados.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Gráfico: Série Temporal dos Últimos 100 Pontos
    fig, ax = plt.subplots(figsize=(14, 8))

    # Pegar últimos 100 pontos para visualização
    n_pontos = min(100, len(y_test))
    indices = range(len(y_test) - n_pontos, len(y_test))

    ax.plot(indices, y_test[-n_pontos:], 'o-', color='blue', linewidth=2,
            markersize=4, label='Valores Reais', alpha=0.8)
    ax.plot(indices, y_pred[-n_pontos:], 's-', color='red', linewidth=2,
            markersize=4, label='Valores Estimados', alpha=0.8)

    ax.set_xlabel('Índice da Amostra', fontsize=12)
    ax.set_ylabel('Variação % da Média OHLC', fontsize=12)
    ax.set_title(f'Estimador LSTM: Série Temporal (Últimos {n_pontos} Pontos)\n' +
                f'Comparação entre Variações % Reais e Estimadas', fontsize=14, fontweight='bold')
    ax.legend(loc='upper left')



def main():
    setup_environment()

    print("🤖 CLASSIFICADOR LSTM BINÁRIO - SINAIS DE TRADING")
    print("=" * 80)
    print("📊 Baseado no pct_change da média OHLC das ações diversificadas")
    print("🎯 Modelo binário: apenas classes Compra/Venda")

    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    data_period = config.get('xgboost.data_period')

    print(f"🎯 Sinais: Compra/Venda baseados em {signal_horizon} dias à frente")
    print(f"🔧 Features básicas: pct_change da Média OHLC passada ({ohlc_lags} dias), Volume, Spread, Volatilidade, dia da semana, mês")
    print(f"🔬 Features econométricas: Volatilidade de Parkinson, MFI, EMV, Amihud, Roll Spread,")
    print(f"   Hurst, Volume, CMF, A/D Line, Volume Oscillator")
    print(f"📅 Período de dados: {data_period}")
    print("=" * 80)

    # Carregar lista de ações diversificadas
    acoes = carregar_acoes_diversificadas()
    periodo = config.get('xgboost.data_period', '5y')
    import yfinance as yf

    print(f"\n📥 Baixando dados de {len(acoes)} ações...")
    acoes_dados = {}
    for ticker, nome in acoes:
        print(f"   • {ticker} ({nome})")
        dados = yf.download(ticker, period=periodo, progress=False)
        if dados is None or len(dados) == 0:
            print(f"     ❌ Falha ao baixar dados de {ticker}")
            continue
        # Padronizar nomes das colunas
        colunas_esperadas = ['Open', 'High', 'Low', 'Close', 'Volume']
        def normalizar_nome_col(col):
            nome_col = col[0] if isinstance(col, tuple) else col
            if isinstance(nome_col, str) and nome_col.lower() in [c.lower() for c in colunas_esperadas]:
                return nome_col.capitalize()
            return nome_col
        dados.columns = [normalizar_nome_col(col) for col in dados.columns]
        # Calcular features e sinais
        dados = calcular_features_e_sinais(dados, ticker=ticker)
        print(f"     ➡️ Registros após cálculo de features: {len(dados)}")
        if len(dados) == 0:
            print(f"     ⚠️ Nenhum registro válido após cálculo de features para {ticker}")
        acoes_dados[ticker] = dados

    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Unificar todos os dados em um único DataFrame
    print(f"\n🔧 Preparando dataset combinado para treinamento...")
    todos_dfs = []
    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados = dados.copy()
            dados['Ticker'] = ticker
            todos_dfs.append(dados)
    if not todos_dfs:
        print("❌ Nenhum dado disponível para treinamento.")
        return
    dataset = pd.concat(todos_dfs, ignore_index=True)

    # Selecionar features e target
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    quarter_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    holiday_features = ['Pre_Feriado_Brasil']
    basic_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)] + weekday_features + month_features + quarter_features + holiday_features
    econometric_features_all = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
        'High_Max_50', 'Low_Min_50'
    ]
    econometric_features_lagged = []
    for feature in econometric_features_all:
        for i in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{i}')
    feature_cols = basic_features + econometric_features_lagged
    feature_cols = [col for col in feature_cols if col in dataset.columns]
    target_col = 'Sinal_Compra'

    # Remover NaN
    dataset = dataset.dropna(subset=feature_cols + [target_col])

    # Normalizar features
    scaler = StandardScaler()
    dataset[feature_cols] = scaler.fit_transform(dataset[feature_cols])

    # Divisão temporal (80% treino, 20% teste)
    test_size = config.get('xgboost.test_size', 0.2)
    split_idx = int(len(dataset) * (1 - test_size))
    dataset = dataset.reset_index(drop=True)
    sequence_length = config.get('lstm.sequence_length', 20)
    X, y = preparar_dados_lstm(dataset, feature_cols, target_col, sequence_length=sequence_length)
    X_train, X_test = X[:split_idx-sequence_length], X[split_idx-sequence_length:]
    y_train, y_test = y[:split_idx-sequence_length], y[split_idx-sequence_length:]

    print(f"\n📊 Dados para LSTM:")
    print(f"   • Treino: {X_train.shape[0]} sequências")
    print(f"   • Teste: {X_test.shape[0]} sequências")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Sequence length: {sequence_length}")

    # Treinar modelo LSTM
    print("\n🚀 Treinando modelo LSTM binário...")
    model, history = treinar_lstm_binario(
        X_train, y_train, X_test, y_test, input_shape=(sequence_length, len(feature_cols)),
        epochs=config.get('lstm.epochs', 50),
        batch_size=config.get('lstm.batch_size', 32)
    )

    # Avaliar modelo
    y_pred_proba = model.predict(X_test)
    y_pred = (y_pred_proba > 0.5).astype(int).flatten()
    accuracy = accuracy_score(y_test, y_pred)
    print(f"\n🎯 Acurácia no conjunto de teste: {accuracy:.3f}")
    print(f"\nRelatório de classificação:")
    print(classification_report(y_test, y_pred, target_names=['Venda', 'Compra']))

    # Estatísticas das classes
    unique, counts = np.unique(y_test, return_counts=True)
    class_stats = dict(zip(unique, counts))
    print(f"\nDistribuição das classes no teste:")
    print(f"   • Classe 0 (Venda): {class_stats.get(0, 0)} ({class_stats.get(0, 0)/len(y_test)*100:.1f}%)")
    print(f"   • Classe 1 (Compra): {class_stats.get(1, 0)} ({class_stats.get(1, 0)/len(y_test)*100:.1f}%)")

if __name__ == "__main__":
    main()
